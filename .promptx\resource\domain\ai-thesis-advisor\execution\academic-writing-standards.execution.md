<execution>
  <constraint>
    ## 学术写作的客观约束
    - **期刊规范**：不同期刊有特定的格式和风格要求
    - **学科传统**：AI领域有其特定的写作惯例和表达方式
    - **篇幅限制**：通常有严格的字数或页数限制
    - **时间约束**：需要在有限时间内完成高质量写作
    - **语言要求**：需要准确、简洁、专业的学术英语表达
  </constraint>

  <rule>
    ## 学术写作强制规则
    - **原创性要求**：所有内容必须是原创或正确引用
    - **逻辑一致性**：全文逻辑必须严密、前后一致
    - **证据支撑**：所有论点必须有充分的证据支持
    - **格式规范性**：严格遵循目标期刊的格式要求
    - **语言准确性**：术语使用准确，表达清晰无歧义
  </rule>

  <guideline>
    ## 学术写作指导原则
    - **读者导向**：始终考虑读者的理解需求和阅读体验
    - **简洁明了**：用最少的词汇表达最准确的意思
    - **逻辑清晰**：确保论证过程清晰易懂
    - **客观中性**：保持学术写作的客观性和中性立场
    - **创新突出**：明确突出研究的创新点和贡献
  </guideline>

  <process>
    ## 学术写作标准化流程

    ### Step 1: 写作规划与结构设计
    
    ```mermaid
    flowchart TD
        A[确定写作目标] --> B[分析目标读者]
        B --> C[设计整体结构]
        C --> D[制定写作大纲]
        D --> E[分配篇幅比例]
        E --> F[确定写作时间表]
    ```
    
    **AI论文标准结构**：
    1. **Abstract** (150-250词)
       - 研究背景 (1-2句)
       - 问题陈述 (1-2句)
       - 方法概述 (2-3句)
       - 主要结果 (2-3句)
       - 结论意义 (1-2句)
    
    2. **Introduction** (15-20%)
       - 研究背景和动机
       - 相关工作简述
       - 研究问题和挑战
       - 主要贡献和创新点
       - 论文结构安排
    
    3. **Related Work** (10-15%)
       - 按主题分类综述
       - 突出现有方法的局限性
       - 明确本研究的定位
    
    4. **Methodology** (25-30%)
       - 问题形式化定义
       - 方法设计思路
       - 算法详细描述
       - 理论分析（如适用）
    
    5. **Experiments** (25-30%)
       - 实验设置和数据集
       - 评估指标和基线方法
       - 实验结果和分析
       - 消融研究和案例分析
    
    6. **Conclusion** (5-10%)
       - 主要贡献总结
       - 局限性讨论
       - 未来工作方向

    ### Step 2: 段落级写作优化
    
    ```mermaid
    graph LR
        A[主题句] --> B[支撑句]
        B --> C[证据/例子]
        C --> D[分析/解释]
        D --> E[过渡/总结]
    ```
    
    **段落写作标准**：
    - **主题句明确**：每段开头明确表达段落主题
    - **逻辑展开**：按照逻辑顺序展开论述
    - **证据充分**：提供足够的证据支撑观点
    - **过渡自然**：段落间过渡自然流畅
    - **长度适中**：一般5-8句，避免过长或过短

    ### Step 3: 句子级语言优化
    
    **学术写作语言特征**：
    - **客观性**：使用第三人称，避免主观表达
    - **准确性**：术语使用准确，避免模糊表达
    - **简洁性**：删除冗余词汇，保持表达简洁
    - **正式性**：使用正式的学术语言风格
    - **一致性**：术语和表达方式保持一致

    ### Step 4: 引用和参考文献管理
    
    ```mermaid
    flowchart LR
        A[文献收集] --> B[文献筛选]
        B --> C[引用标注]
        C --> D[参考文献列表]
        D --> E[格式检查]
        E --> F[完整性验证]
    ```
    
    **引用规范要求**：
    - **引用完整性**：所有引用都有对应的参考文献
    - **引用准确性**：引用内容与原文一致
    - **引用时效性**：优先引用近年来的重要工作
    - **引用平衡性**：避免过度自引或偏向性引用
    - **格式一致性**：严格按照期刊要求格式化

    ### Step 5: 图表设计与优化
    
    **图表质量标准**：
    - **清晰度**：图表内容清晰可读
    - **完整性**：包含必要的标题、标签、图例
    - **一致性**：风格和格式保持一致
    - **相关性**：与正文内容紧密相关
    - **专业性**：符合学术出版标准

    ## 常见写作问题及解决方案

    ### 结构问题
    ```mermaid
    mindmap
      root((结构问题))
        逻辑混乱
          重新梳理论证逻辑
          使用思维导图规划
        重点不突出
          明确核心贡献
          调整篇幅分配
        过渡生硬
          增加过渡句
          优化段落连接
    ```

    ### 语言问题
    - **表达不准确** → 使用专业术语，避免口语化
    - **句式单调** → 变化句式结构，增加表达丰富性
    - **逻辑词缺失** → 适当使用逻辑连接词
    - **时态不一致** → 统一时态使用规范

    ### 内容问题
    - **创新点不明确** → 在多处明确强调创新贡献
    - **实验不充分** → 增加对比实验和消融研究
    - **分析不深入** → 深入分析结果的原因和意义
    - **相关工作不全面** → 补充最新相关研究
  </process>

  <criteria>
    ## 学术写作质量评价标准

    ### 内容质量
    - ✅ **创新性明确**：研究贡献清晰突出
    - ✅ **逻辑严密**：论证过程逻辑清晰
    - ✅ **证据充分**：实验数据支撑有力
    - ✅ **分析深入**：结果分析透彻全面

    ### 表达质量
    - ✅ **语言准确**：术语使用准确无误
    - ✅ **表达清晰**：意思表达明确易懂
    - ✅ **风格一致**：全文风格统一专业
    - ✅ **语法正确**：无语法和拼写错误

    ### 格式规范
    - ✅ **结构标准**：符合学术论文结构要求
    - ✅ **引用规范**：引用格式完全正确
    - ✅ **图表专业**：图表设计专业美观
    - ✅ **版式整齐**：整体版式规范整洁

    ### 读者体验
    - ✅ **易于理解**：读者能够轻松理解内容
    - ✅ **逻辑清晰**：论证过程清晰易跟随
    - ✅ **重点突出**：核心贡献明显突出
    - ✅ **阅读流畅**：整体阅读体验良好
  </criteria>
</execution>
