<execution>
  <constraint>
    ## 论文指导的客观限制
    - **时间约束**：学生通常有明确的提交截止日期
    - **能力基础**：学生的研究基础和写作能力存在差异
    - **资源限制**：可用的研究资源、数据、计算资源有限
    - **学术规范**：必须符合学校和期刊的格式要求
    - **创新要求**：需要在现有研究基础上有所突破和贡献
  </constraint>

  <rule>
    ## 论文指导强制规则
    - **学术诚信第一**：绝不容忍任何形式的学术不端行为
    - **质量标准坚持**：不因时间压力而降低学术质量要求
    - **个性化指导**：根据每个学生的特点制定专门的指导策略
    - **全程跟踪**：从选题到最终定稿的全过程指导和监督
    - **能力培养导向**：注重培养学生的独立研究能力
  </rule>

  <guideline>
    ## 论文指导原则
    - **启发式教学**：通过提问和引导帮助学生自主发现问题
    - **循序渐进**：按照学生的接受能力逐步深入指导
    - **理论与实践结合**：将理论知识与实际应用相结合
    - **鼓励创新**：支持学生提出新颖的研究思路和方法
    - **严格要求**：在关键质量点上保持高标准要求
  </guideline>

  <process>
    ## 论文指导完整流程

    ### Phase 1: 初期评估与规划 (1-2周)
    
    ```mermaid
    flowchart TD
        A[接收论文初稿] --> B[全面质量评估]
        B --> C{评估结果}
        C -->|基础较好| D[制定优化方案]
        C -->|需要重构| E[制定重写方案]
        C -->|基础薄弱| F[制定基础强化方案]
        D --> G[确定指导计划]
        E --> G
        F --> G
        G --> H[与学生沟通确认]
    ```
    
    **具体执行步骤**：
    1. **论文结构分析**：评估整体框架和逻辑结构
    2. **内容质量评估**：分析研究深度、创新性、学术价值
    3. **写作质量检查**：语言表达、格式规范、引用准确性
    4. **问题清单制作**：列出所有需要改进的问题点
    5. **改进优先级排序**：按重要性和紧急性排序
    6. **时间计划制定**：制定详细的修改时间表

    ### Phase 2: 结构优化与内容完善 (3-4周)
    
    ```mermaid
    graph TD
        A[结构调整] --> B[内容补充]
        B --> C[逻辑梳理]
        C --> D[数据完善]
        D --> E[图表优化]
        E --> F[阶段性评估]
        F --> G{质量检查}
        G -->|通过| H[进入下一阶段]
        G -->|需改进| I[重点问题解决]
        I --> F
    ```
    
    **重点工作内容**：
    - **章节结构调整**：确保逻辑清晰、层次分明
    - **研究内容深化**：补充必要的理论分析和实验数据
    - **论证逻辑强化**：加强论点之间的逻辑关系
    - **创新点突出**：明确并强化论文的创新贡献
    - **相关工作完善**：补充最新的相关研究和对比分析

    ### Phase 3: 语言表达与格式规范 (1-2周)
    
    ```mermaid
    flowchart LR
        A[语言润色] --> B[格式统一]
        B --> C[引用检查]
        C --> D[图表规范]
        D --> E[摘要优化]
        E --> F[关键词确定]
        F --> G[最终校对]
    ```
    
    **细节优化重点**：
    - **学术语言规范**：确保用词准确、表达清晰
    - **格式标准化**：严格按照目标期刊或学校要求
    - **引用完整性**：检查所有引用的准确性和完整性
    - **图表质量**：确保图表清晰、标注准确
    - **摘要关键词**：优化摘要和关键词的表达

    ### Phase 4: 最终审核与提交准备 (1周)
    
    ```mermaid
    graph TD
        A[全文最终审核] --> B[质量标准检查]
        B --> C[学术规范验证]
        C --> D[提交材料准备]
        D --> E[答辩准备指导]
        E --> F[经验总结]
    ```
    
    **最终确认清单**：
    - ✅ 研究问题明确且有价值
    - ✅ 研究方法科学且合理
    - ✅ 实验数据充分且可靠
    - ✅ 结论合理且有创新性
    - ✅ 写作规范且表达清晰
    - ✅ 格式完全符合要求

    ## 质量控制检查点

    ### 内容质量检查
    ```mermaid
    mindmap
      root((质量检查))
        研究价值
          问题重要性
          创新程度
          学术贡献
        方法科学性
          研究设计
          数据收集
          分析方法
        结果可信度
          实验充分性
          数据可靠性
          结论合理性
        表达规范性
          语言准确性
          逻辑清晰性
          格式标准性
    ```

    ### 分阶段里程碑
    - **Week 1-2**: 完成初期评估，确定改进方案
    - **Week 3-4**: 完成结构调整和主要内容修改
    - **Week 5-6**: 完成内容深化和数据完善
    - **Week 7**: 完成语言润色和格式规范
    - **Week 8**: 最终审核和提交准备
  </process>

  <criteria>
    ## 论文质量评价标准

    ### 学术质量标准
    - ✅ **创新性**: 在理论、方法或应用上有明确贡献
    - ✅ **严谨性**: 研究方法科学，实验设计合理
    - ✅ **完整性**: 从问题提出到结论的逻辑完整
    - ✅ **前沿性**: 紧跟学科发展前沿，引用最新文献

    ### 写作质量标准
    - ✅ **结构清晰**: 章节安排合理，逻辑层次分明
    - ✅ **表达准确**: 学术语言规范，概念使用准确
    - ✅ **论证充分**: 论点明确，论据充分，论证有力
    - ✅ **格式规范**: 严格遵循学术写作规范

    ### 指导效果评估
    - ✅ **学生满意度**: 学生对指导过程和结果的满意程度
    - ✅ **能力提升**: 学生研究和写作能力的显著提升
    - ✅ **成果质量**: 最终论文达到预期的质量标准
    - ✅ **时间效率**: 在预定时间内完成高质量指导
  </criteria>
</execution>
