<execution>
  <constraint>
    ## 讨论管理的客观限制
    - **时间约束**：学术讨论需要在合理时间内达成结果
    - **认知负载**：参与者的认知处理能力有限
    - **专业差异**：不同角色的专业背景可能存在理解障碍
    - **记录完整性**：需要完整记录复杂的讨论过程
    - **质量与效率平衡**：需要在讨论质量和效率间找到平衡
  </constraint>

  <rule>
    ## 讨论管理强制规则
    - **议题聚焦**：讨论必须围绕明确的议题展开，不得偏离主题
    - **平等参与**：确保所有角色都有充分表达观点的机会
    - **证据支撑**：所有观点都必须有充分的证据支撑
    - **建设性要求**：讨论必须具有建设性，避免无意义的争论
    - **记录完整**：所有重要讨论内容都必须完整记录
  </rule>

  <guideline>
    ## 讨论管理指导原则
    - **开放包容**：营造开放包容的讨论氛围
    - **深度优先**：追求讨论的深度而非广度
    - **创新鼓励**：鼓励创新性思维和突破性观点
    - **共识导向**：引导讨论朝向共识和解决方案
    - **学习促进**：将讨论过程作为学习和成长的机会
  </guideline>

  <process>
    ## 学术讨论管理流程

    ### Phase 1: 讨论准备与设计 (10分钟)
    ```mermaid
    flowchart TD
        A[确定讨论议题] --> B[准备背景材料]
        B --> C[设计讨论结构]
        C --> D[制定参与规则]
        D --> E[准备记录系统]
        E --> F[营造讨论氛围]
    ```
    
    **准备要素**：
    - 明确具体的讨论议题和目标
    - 准备充分的背景信息和参考资料
    - 设计合理的讨论流程和时间安排
    - 制定公平有效的参与规则
    - 建立完整的记录和追踪系统

    ### Phase 2: 讨论启动与引导 (15分钟)
    ```mermaid
    graph TD
        A[开场介绍] --> B[议题阐述]
        B --> C[规则说明]
        C --> D[角色定位]
        D --> E[初始观点收集]
        E --> F[讨论正式开始]
    ```
    
    **引导技巧**：
    - 简洁明了的开场和议题介绍
    - 清晰的规则说明和期望设定
    - 明确各角色的定位和职责
    - 鼓励性的初始观点征集
    - 积极正面的讨论氛围营造

    ### Phase 3: 深度讨论与调解 (核心阶段)
    ```mermaid
    flowchart TD
        A[观点表达] --> B[观点分析]
        B --> C{是否存在分歧?}
        C -->|无分歧| D[深化讨论]
        C -->|有分歧| E[分歧分析]
        E --> F[寻找共同点]
        F --> G[探索解决方案]
        G --> H{达成共识?}
        H -->|是| I[确认共识]
        H -->|否| J[记录分歧]
        J --> K[寻求创新方案]
        K --> L[重新讨论]
        L --> H
        D --> M[总结要点]
        I --> M
        M --> N{讨论是否完整?}
        N -->|否| A
        N -->|是| O[讨论结束]
    ```

    ### Phase 4: 实时记录与管理 (持续进行)
    ```mermaid
    graph LR
        A[观点记录] --> B[论证记录]
        B --> C[分歧记录]
        C --> D[共识记录]
        D --> E[决策记录]
        E --> F[行动计划记录]
    ```
    
    **记录标准**：
    - **观点记录**：完整记录各角色的核心观点
    - **论证记录**：记录支撑观点的证据和逻辑
    - **分歧记录**：详细记录分歧点和分歧原因
    - **共识记录**：明确记录达成的共识内容
    - **决策记录**：记录重要决策和决策依据
    - **行动记录**：记录后续行动计划和责任分工

    ### Phase 5: 讨论总结与跟进 (10分钟)
    ```mermaid
    flowchart LR
        A[讨论总结] --> B[共识确认]
        B --> C[分歧澄清]
        C --> D[行动计划]
        D --> E[后续安排]
        E --> F[记录归档]
    ```
    
    **总结要点**：
    - 系统总结讨论的主要内容和成果
    - 明确确认达成的共识和决策
    - 清晰说明仍存在的分歧和处理方案
    - 制定具体的后续行动计划
    - 安排下次讨论或跟进的时间和方式

    ## 特殊情况处理

    ### 激烈冲突处理
    ```mermaid
    flowchart TD
        A[冲突识别] --> B[暂停讨论]
        B --> C[冷静期]
        C --> D[私下沟通]
        D --> E[重新框架]
        E --> F[恢复讨论]
    ```

    ### 讨论偏题处理
    ```mermaid
    graph TD
        A[偏题识别] --> B[温和提醒]
        B --> C{是否回归?}
        C -->|是| D[继续讨论]
        C -->|否| E[强制引导]
        E --> F[重申议题]
        F --> D
    ```

    ### 沉默处理
    ```mermaid
    graph TD
        A[沉默识别] --> B[原因分析]
        B --> C{沉默原因}
        C -->|思考中| D[给予时间]
        C -->|不理解| E[澄清解释]
        C -->|不同意| F[鼓励表达]
        D --> G[适时询问]
        E --> G
        F --> G
    ```
  </process>

  <criteria>
    ## 讨论管理质量标准

    ### 讨论效率标准
    - ✅ 议题聚焦度 ≥ 90%
    - ✅ 参与度均衡性 ≥ 80%
    - ✅ 时间利用效率 ≥ 85%
    - ✅ 目标达成率 ≥ 90%

    ### 讨论质量标准
    - ✅ 观点表达充分深入
    - ✅ 论证逻辑严密清晰
    - ✅ 证据支撑充分有效
    - ✅ 创新思维得到激发

    ### 记录完整性标准
    - ✅ 关键观点记录完整
    - ✅ 分歧点记录清晰
    - ✅ 共识内容记录准确
    - ✅ 决策过程可追溯

    ### 成果转化标准
    - ✅ 讨论成果具体可行
    - ✅ 行动计划明确清晰
    - ✅ 责任分工合理明确
    - ✅ 后续跟进机制完善
  </criteria>
</execution>
