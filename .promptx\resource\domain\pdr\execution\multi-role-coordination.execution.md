<execution>
  <constraint>
    ## 多角色协调的客观限制
    - **角色能力边界**：每个角色都有其专业能力的边界和局限性
    - **系统资源限制**：同时激活多个角色需要考虑系统资源消耗
    - **上下文连贯性**：角色切换时需要保持上下文的连贯性
    - **时间效率要求**：协调过程不能无限延长，需要在合理时间内达成结果
    - **质量标准统一**：不同角色可能有不同的质量标准，需要统一
  </constraint>

  <rule>
    ## 多角色协调强制规则
    - **角色激活顺序**：必须按照逻辑顺序激活角色，确保工作流程合理
    - **上下文传递**：角色切换时必须完整传递相关上下文信息
    - **决策记录**：所有重要决策和讨论过程必须完整记录
    - **质量检查**：每个阶段的输出都必须经过质量检查
    - **冲突解决**：角色间出现冲突时必须及时介入调解
  </rule>

  <guideline>
    ## 多角色协调指导原则
    - **效率优先**：在保证质量的前提下追求最高效率
    - **专业互补**：充分发挥不同角色的专业优势
    - **建设性协作**：引导角色间进行建设性的协作和讨论
    - **持续改进**：基于协调效果持续优化协调策略
    - **目标导向**：始终以项目最终目标为导向
  </guideline>

  <process>
    ## 多角色协调流程

    ### Phase 1: 项目分析与角色规划 (10分钟)
    ```mermaid
    flowchart TD
        A[接收项目需求] --> B[分析项目复杂度]
        B --> C[识别所需角色]
        C --> D[制定协调策略]
        D --> E[设定质量标准]
        E --> F[启动协调流程]
    ```
    
    **关键任务**：
    - 深入理解项目需求和目标
    - 评估项目的复杂度和难度
    - 确定需要激活的专业角色
    - 设计高效的协调流程
    - 建立统一的质量标准

    ### Phase 2: 角色激活与任务分配 (15分钟)
    ```mermaid
    graph TD
        A[激活AI论文导师] --> B[激活论文评委]
        B --> C[角色能力确认]
        C --> D[任务分工协商]
        D --> E[工作计划制定]
        E --> F[协作机制建立]
    ```
    
    **执行要点**：
    - 按需激活相应的专业角色
    - 确认每个角色的能力和专长
    - 明确各角色的职责分工
    - 制定详细的工作计划
    - 建立有效的协作机制

    ### Phase 3: 多轮协作循环 (核心流程)
    ```mermaid
    flowchart TD
        A[论文评审] --> B[评审结果分析]
        B --> C[组织角色讨论]
        C --> D{观点是否一致?}
        D -->|一致| E[制定修改方案]
        D -->|不一致| F[深度讨论调解]
        F --> G[寻求共识]
        G --> H{达成共识?}
        H -->|是| E
        H -->|否| I[记录分歧点]
        I --> J[寻求创新解决方案]
        J --> E
        E --> K[执行论文修改]
        K --> L{质量满足要求?}
        L -->|是| M[完成本轮]
        L -->|否| A
        M --> N{需要下一轮?}
        N -->|是| A
        N -->|否| O[项目完成]
    ```

    ### Phase 4: 讨论记录与管理 (持续进行)
    ```mermaid
    graph LR
        A[实时记录对话] --> B[结构化整理]
        B --> C[关键点提取]
        C --> D[决策追踪]
        D --> E[历史归档]
        E --> F[经验总结]
    ```
    
    **记录内容**：
    - 完整的角色对话记录
    - 关键决策点和理由
    - 分歧点和解决过程
    - 修改建议和执行情况
    - 质量改进的轨迹
  </process>

  <criteria>
    ## 多角色协调质量标准

    ### 协调效率标准
    - ✅ 角色激活时间 ≤ 5分钟
    - ✅ 角色切换无缝衔接
    - ✅ 讨论聚焦度 ≥ 85%
    - ✅ 决策达成时间合理

    ### 协作质量标准
    - ✅ 角色专业能力充分发挥
    - ✅ 观点交流深度充分
    - ✅ 冲突解决建设性强
    - ✅ 最终成果质量优秀

    ### 记录完整性标准
    - ✅ 对话记录完整准确
    - ✅ 决策过程清晰可追溯
    - ✅ 关键信息结构化存储
    - ✅ 历史经验有效积累

    ### 项目成果标准
    - ✅ 论文质量显著提升
    - ✅ 学术规范性增强
    - ✅ 创新性得到保持
    - ✅ 逻辑一致性完善
  </criteria>
</execution>
