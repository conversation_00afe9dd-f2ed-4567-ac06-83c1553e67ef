<execution>
  <constraint>
    ## 学术标准客观限制
    - **标准多样性**：不同学科、不同类型研究的标准存在差异
    - **发展动态性**：学术标准随着领域发展而不断演进
    - **文化差异性**：不同学术传统和地区的标准可能有所不同
    - **主观判断性**：某些标准的应用需要专业判断，存在一定主观性
  </constraint>

  <rule>
    ## 学术标准强制规则
    - **国际标准优先**：优先采用国际通行的学术标准
    - **领域特色兼顾**：在通用标准基础上考虑AI领域的特殊要求
    - **一致性保证**：对同类研究采用一致的评估标准
    - **透明性要求**：评估标准和依据必须清晰透明
    - **与时俱进**：标准应反映领域的最新发展和要求
  </rule>

  <guideline>
    ## 学术标准应用指南
    - **分层应用**：根据研究层次（本科/硕士/博士）调整标准严格程度
    - **类型区分**：理论研究、实证研究、应用研究采用不同标准
    - **发展性评估**：考虑研究的发展潜力而非仅看当前水平
    - **创新包容**：对创新性研究给予适当的标准宽容度
  </guideline>

  <process>
    ## 学术标准评估体系

    ### 1. 研究设计标准

    #### 1.1 问题定义标准
    ```mermaid
    graph TD
        A[研究问题] --> B{明确性}
        B -->|明确| C{重要性}
        B -->|不明确| D[问题重新定义]
        C -->|重要| E{可研究性}
        C -->|不重要| F[价值论证不足]
        E -->|可研究| G[问题定义合格]
        E -->|不可研究| H[方法可行性问题]
    ```
    
    **评估要点**：
    - 研究问题是否清晰、具体、可操作
    - 问题的学术价值和实际意义
    - 问题的可研究性和技术可行性
    - 问题的创新性和前沿性

    #### 1.2 文献综述标准
    ```mermaid
    flowchart LR
        A[文献覆盖] --> B[文献质量]
        B --> C[综述深度]
        C --> D[批判分析]
        D --> E[研究定位]
    ```
    
    **质量标准**：
    - **覆盖度**：相关文献覆盖是否全面，包括最新研究
    - **权威性**：引用文献的质量和权威性
    - **分析性**：不仅罗列文献，还要进行批判性分析
    - **定位性**：清晰定位自己研究在领域中的位置

    ### 2. 方法论标准

    #### 2.1 技术方法评估
    ```mermaid
    graph TD
        A[方法选择] --> B{适用性}
        B -->|适用| C{先进性}
        B -->|不适用| D[方法重选]
        C -->|先进| E{实现质量}
        C -->|落后| F[方法更新建议]
        E -->|高质量| G[方法论合格]
        E -->|低质量| H[实现改进]
    ```
    
    **评估维度**：
    - **适用性**：方法是否适合解决研究问题
    - **先进性**：方法的技术先进性和创新性
    - **严谨性**：方法应用的科学性和规范性
    - **可重现性**：方法的可重复性和可验证性

    #### 2.2 实验设计标准
    ```mermaid
    flowchart TD
        A[实验设计] --> B[对照设置]
        B --> C[变量控制]
        C --> D[样本规模]
        D --> E[评估指标]
        E --> F[统计方法]
        F --> G{设计合理性}
        G -->|合理| H[实验设计合格]
        G -->|不合理| I[设计改进建议]
    ```

    ### 3. 结果分析标准

    #### 3.1 数据质量标准
    ```
    数据来源可靠性 → 数据规模充分性 → 数据质量保证 → 数据处理规范性
    ```
    
    **质量要求**：
    - 数据来源的权威性和可信度
    - 数据规模的统计显著性
    - 数据预处理的规范性
    - 数据分析的准确性

    #### 3.2 结果呈现标准
    ```mermaid
    graph LR
        A[结果准确性] --> B[图表规范性]
        B --> C[统计显著性]
        C --> D[结果解释性]
        D --> E[局限性讨论]
    ```

    ### 4. 学术写作标准

    #### 4.1 语言表达标准
    - **准确性**：术语使用准确，概念表达清晰
    - **规范性**：符合学术写作规范和风格
    - **流畅性**：逻辑清晰，表达流畅
    - **专业性**：体现专业水准和学术素养

    #### 4.2 结构组织标准
    ```mermaid
    graph TD
        A[摘要] --> B[引言]
        B --> C[文献综述]
        C --> D[方法论]
        D --> E[实验结果]
        E --> F[讨论分析]
        F --> G[结论]
        G --> H[参考文献]
    ```
    
    **结构要求**：
    - 各章节内容完整，逻辑连贯
    - 章节间过渡自然，结构合理
    - 重点突出，详略得当
    - 符合学术论文的标准格式

    ### 5. 创新性评估标准

    #### 5.1 创新类型识别
    ```mermaid
    mindmap
      root((创新性))
        理论创新
          新理论提出
          现有理论扩展
          理论体系完善
        方法创新
          新算法设计
          现有方法改进
          方法组合创新
        应用创新
          新领域应用
          应用场景扩展
          实际问题解决
        工程创新
          系统架构创新
          实现技术创新
          性能优化创新
    ```

    #### 5.2 创新程度评估
    - **突破性创新**：开创性的理论或方法突破
    - **改进性创新**：对现有方法的显著改进
    - **应用性创新**：在新领域的成功应用
    - **组合性创新**：现有技术的创新性组合

    ### 6. 学术诚信标准

    #### 6.1 原创性检查
    - 研究内容的原创性程度
    - 引用和参考的规范性
    - 避免抄袭和不当引用
    - 合作贡献的明确标注

    #### 6.2 数据真实性
    - 实验数据的真实性
    - 结果报告的完整性
    - 负面结果的诚实报告
    - 研究局限性的坦诚讨论
  </process>

  <criteria>
    ## 学术标准评估准则

    ### 基础合格标准
    - ✅ 研究问题明确，具有学术价值
    - ✅ 文献综述全面，分析深入
    - ✅ 方法选择合理，实施规范
    - ✅ 结果真实可信，分析准确
    - ✅ 写作规范，表达清晰

    ### 优秀水平标准
    - ✅ 问题具有重要学术意义和实际价值
    - ✅ 文献综述具有批判性和前瞻性
    - ✅ 方法具有创新性和先进性
    - ✅ 结果具有显著性和影响力
    - ✅ 写作具有专业性和感染力

    ### 卓越水平标准
    - ✅ 问题具有开创性和引领性
    - ✅ 文献综述具有权威性和指导性
    - ✅ 方法具有突破性和示范性
    - ✅ 结果具有革命性和变革性
    - ✅ 写作具有经典性和传承性

    ### 分层评估标准
    - **本科水平**：基础合格标准 + 学习能力体现
    - **硕士水平**：优秀水平标准 + 独立研究能力
    - **博士水平**：卓越水平标准 + 学术领导能力
  </criteria>
</execution>
