{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-24T21:54:10.624Z", "updatedAt": "2025-07-24T21:54:10.638Z", "resourceCount": 27}, "resources": [{"id": "ai-thesis-advisor", "source": "project", "protocol": "role", "name": "Ai Thesis Advisor 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/ai-thesis-advisor/ai-thesis-advisor.role.md", "metadata": {"createdAt": "2025-07-24T21:54:10.626Z", "updatedAt": "2025-07-24T21:54:10.626Z", "scannedAt": "2025-07-24T21:54:10.626Z", "path": "domain/ai-thesis-advisor/ai-thesis-advisor.role.md"}}, {"id": "academic-writing-standards", "source": "project", "protocol": "execution", "name": "Academic Writing Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ai-thesis-advisor/execution/academic-writing-standards.execution.md", "metadata": {"createdAt": "2025-07-24T21:54:10.627Z", "updatedAt": "2025-07-24T21:54:10.627Z", "scannedAt": "2025-07-24T21:54:10.627Z", "path": "domain/ai-thesis-advisor/execution/academic-writing-standards.execution.md"}}, {"id": "student-development-approach", "source": "project", "protocol": "execution", "name": "Student Development Approach 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ai-thesis-advisor/execution/student-development-approach.execution.md", "metadata": {"createdAt": "2025-07-24T21:54:10.627Z", "updatedAt": "2025-07-24T21:54:10.627Z", "scannedAt": "2025-07-24T21:54:10.627Z", "path": "domain/ai-thesis-advisor/execution/student-development-approach.execution.md"}}, {"id": "thesis-guidance-workflow", "source": "project", "protocol": "execution", "name": "Thesis Guidance Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ai-thesis-advisor/execution/thesis-guidance-workflow.execution.md", "metadata": {"createdAt": "2025-07-24T21:54:10.627Z", "updatedAt": "2025-07-24T21:54:10.627Z", "scannedAt": "2025-07-24T21:54:10.627Z", "path": "domain/ai-thesis-advisor/execution/thesis-guidance-workflow.execution.md"}}, {"id": "academic-writing-mastery", "source": "project", "protocol": "knowledge", "name": "Academic Writing Mastery 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ai-thesis-advisor/knowledge/academic-writing-mastery.knowledge.md", "metadata": {"createdAt": "2025-07-24T21:54:10.628Z", "updatedAt": "2025-07-24T21:54:10.628Z", "scannedAt": "2025-07-24T21:54:10.628Z", "path": "domain/ai-thesis-advisor/knowledge/academic-writing-mastery.knowledge.md"}}, {"id": "ai-domain-expertise", "source": "project", "protocol": "knowledge", "name": "Ai Domain Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ai-thesis-advisor/knowledge/ai-domain-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-24T21:54:10.628Z", "updatedAt": "2025-07-24T21:54:10.628Z", "scannedAt": "2025-07-24T21:54:10.628Z", "path": "domain/ai-thesis-advisor/knowledge/ai-domain-expertise.knowledge.md"}}, {"id": "thesis-structure-optimization", "source": "project", "protocol": "knowledge", "name": "Thesis Structure Optimization 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ai-thesis-advisor/knowledge/thesis-structure-optimization.knowledge.md", "metadata": {"createdAt": "2025-07-24T21:54:10.629Z", "updatedAt": "2025-07-24T21:54:10.629Z", "scannedAt": "2025-07-24T21:54:10.629Z", "path": "domain/ai-thesis-advisor/knowledge/thesis-structure-optimization.knowledge.md"}}, {"id": "academic-mentoring", "source": "project", "protocol": "thought", "name": "Academic Mentoring 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ai-thesis-advisor/thought/academic-mentoring.thought.md", "metadata": {"createdAt": "2025-07-24T21:54:10.629Z", "updatedAt": "2025-07-24T21:54:10.629Z", "scannedAt": "2025-07-24T21:54:10.629Z", "path": "domain/ai-thesis-advisor/thought/academic-mentoring.thought.md"}}, {"id": "ai-research-thinking", "source": "project", "protocol": "thought", "name": "Ai Research Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ai-thesis-advisor/thought/ai-research-thinking.thought.md", "metadata": {"createdAt": "2025-07-24T21:54:10.630Z", "updatedAt": "2025-07-24T21:54:10.630Z", "scannedAt": "2025-07-24T21:54:10.630Z", "path": "domain/ai-thesis-advisor/thought/ai-research-thinking.thought.md"}}, {"id": "discussion-management", "source": "project", "protocol": "execution", "name": "Discussion Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/pdr/execution/discussion-management.execution.md", "metadata": {"createdAt": "2025-07-24T21:54:10.631Z", "updatedAt": "2025-07-24T21:54:10.631Z", "scannedAt": "2025-07-24T21:54:10.631Z", "path": "domain/pdr/execution/discussion-management.execution.md"}}, {"id": "multi-role-coordination", "source": "project", "protocol": "execution", "name": "Multi Role Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/pdr/execution/multi-role-coordination.execution.md", "metadata": {"createdAt": "2025-07-24T21:54:10.631Z", "updatedAt": "2025-07-24T21:54:10.631Z", "scannedAt": "2025-07-24T21:54:10.631Z", "path": "domain/pdr/execution/multi-role-coordination.execution.md"}}, {"id": "academic-discussion", "source": "project", "protocol": "knowledge", "name": "Academic Discussion 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/pdr/knowledge/academic-discussion.knowledge.md", "metadata": {"createdAt": "2025-07-24T21:54:10.632Z", "updatedAt": "2025-07-24T21:54:10.632Z", "scannedAt": "2025-07-24T21:54:10.632Z", "path": "domain/pdr/knowledge/academic-discussion.knowledge.md"}}, {"id": "project-coordination", "source": "project", "protocol": "knowledge", "name": "Project Coordination 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/pdr/knowledge/project-coordination.knowledge.md", "metadata": {"createdAt": "2025-07-24T21:54:10.632Z", "updatedAt": "2025-07-24T21:54:10.632Z", "scannedAt": "2025-07-24T21:54:10.632Z", "path": "domain/pdr/knowledge/project-coordination.knowledge.md"}}, {"id": "role-management", "source": "project", "protocol": "knowledge", "name": "Role Management 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/pdr/knowledge/role-management.knowledge.md", "metadata": {"createdAt": "2025-07-24T21:54:10.632Z", "updatedAt": "2025-07-24T21:54:10.632Z", "scannedAt": "2025-07-24T21:54:10.632Z", "path": "domain/pdr/knowledge/role-management.knowledge.md"}}, {"id": "pdr", "source": "project", "protocol": "role", "name": "Pdr 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/pdr/pdr.role.md", "metadata": {"createdAt": "2025-07-24T21:54:10.633Z", "updatedAt": "2025-07-24T21:54:10.633Z", "scannedAt": "2025-07-24T21:54:10.633Z", "path": "domain/pdr/pdr.role.md"}}, {"id": "academic-mediation", "source": "project", "protocol": "thought", "name": "Academic Mediation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/pdr/thought/academic-mediation.thought.md", "metadata": {"createdAt": "2025-07-24T21:54:10.633Z", "updatedAt": "2025-07-24T21:54:10.633Z", "scannedAt": "2025-07-24T21:54:10.633Z", "path": "domain/pdr/thought/academic-mediation.thought.md"}}, {"id": "coordination-thinking", "source": "project", "protocol": "thought", "name": "Coordination Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/pdr/thought/coordination-thinking.thought.md", "metadata": {"createdAt": "2025-07-24T21:54:10.633Z", "updatedAt": "2025-07-24T21:54:10.633Z", "scannedAt": "2025-07-24T21:54:10.633Z", "path": "domain/pdr/thought/coordination-thinking.thought.md"}}, {"id": "dialogue-facilitation", "source": "project", "protocol": "thought", "name": "Dialogue Facilitation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/pdr/thought/dialogue-facilitation.thought.md", "metadata": {"createdAt": "2025-07-24T21:54:10.634Z", "updatedAt": "2025-07-24T21:54:10.634Z", "scannedAt": "2025-07-24T21:54:10.634Z", "path": "domain/pdr/thought/dialogue-facilitation.thought.md"}}, {"id": "academic-standards", "source": "project", "protocol": "execution", "name": "Academic Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/thesis-reviewer/execution/academic-standards.execution.md", "metadata": {"createdAt": "2025-07-24T21:54:10.635Z", "updatedAt": "2025-07-24T21:54:10.635Z", "scannedAt": "2025-07-24T21:54:10.635Z", "path": "domain/thesis-reviewer/execution/academic-standards.execution.md"}}, {"id": "thesis-review-process", "source": "project", "protocol": "execution", "name": "Thesis Review Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/thesis-reviewer/execution/thesis-review-process.execution.md", "metadata": {"createdAt": "2025-07-24T21:54:10.635Z", "updatedAt": "2025-07-24T21:54:10.635Z", "scannedAt": "2025-07-24T21:54:10.635Z", "path": "domain/thesis-reviewer/execution/thesis-review-process.execution.md"}}, {"id": "academic-writing", "source": "project", "protocol": "knowledge", "name": "Academic Writing 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/thesis-reviewer/knowledge/academic-writing.knowledge.md", "metadata": {"createdAt": "2025-07-24T21:54:10.635Z", "updatedAt": "2025-07-24T21:54:10.635Z", "scannedAt": "2025-07-24T21:54:10.635Z", "path": "domain/thesis-reviewer/knowledge/academic-writing.knowledge.md"}}, {"id": "ai-research-domain", "source": "project", "protocol": "knowledge", "name": "Ai Research Domain 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/thesis-reviewer/knowledge/ai-research-domain.knowledge.md", "metadata": {"createdAt": "2025-07-24T21:54:10.636Z", "updatedAt": "2025-07-24T21:54:10.636Z", "scannedAt": "2025-07-24T21:54:10.636Z", "path": "domain/thesis-reviewer/knowledge/ai-research-domain.knowledge.md"}}, {"id": "review-methodology", "source": "project", "protocol": "knowledge", "name": "Review Methodology 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/thesis-reviewer/knowledge/review-methodology.knowledge.md", "metadata": {"createdAt": "2025-07-24T21:54:10.636Z", "updatedAt": "2025-07-24T21:54:10.636Z", "scannedAt": "2025-07-24T21:54:10.636Z", "path": "domain/thesis-reviewer/knowledge/review-methodology.knowledge.md"}}, {"id": "thesis-reviewer", "source": "project", "protocol": "role", "name": "Thesis Reviewer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/thesis-reviewer/thesis-reviewer.role.md", "metadata": {"createdAt": "2025-07-24T21:54:10.636Z", "updatedAt": "2025-07-24T21:54:10.636Z", "scannedAt": "2025-07-24T21:54:10.636Z", "path": "domain/thesis-reviewer/thesis-reviewer.role.md"}}, {"id": "academic-expertise", "source": "project", "protocol": "thought", "name": "Academic Expertise 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/thesis-reviewer/thought/academic-expertise.thought.md", "metadata": {"createdAt": "2025-07-24T21:54:10.637Z", "updatedAt": "2025-07-24T21:54:10.637Z", "scannedAt": "2025-07-24T21:54:10.637Z", "path": "domain/thesis-reviewer/thought/academic-expertise.thought.md"}}, {"id": "constructive-guidance", "source": "project", "protocol": "thought", "name": "Constructive Guidance 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/thesis-reviewer/thought/constructive-guidance.thought.md", "metadata": {"createdAt": "2025-07-24T21:54:10.637Z", "updatedAt": "2025-07-24T21:54:10.637Z", "scannedAt": "2025-07-24T21:54:10.637Z", "path": "domain/thesis-reviewer/thought/constructive-guidance.thought.md"}}, {"id": "critical-thinking", "source": "project", "protocol": "thought", "name": "Critical Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/thesis-reviewer/thought/critical-thinking.thought.md", "metadata": {"createdAt": "2025-07-24T21:54:10.637Z", "updatedAt": "2025-07-24T21:54:10.637Z", "scannedAt": "2025-07-24T21:54:10.637Z", "path": "domain/thesis-reviewer/thought/critical-thinking.thought.md"}}], "stats": {"totalResources": 27, "byProtocol": {"role": 3, "execution": 7, "knowledge": 9, "thought": 8}, "bySource": {"project": 27}}}