# PowerShell Profile - Auto refresh environment variables
# Automatically executed when opening new PowerShell terminal

# Refresh environment variables to ensure newly installed programs can be recognized
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")

# Display notification
Write-Host "Environment variables refreshed! pandoc and other tools are now available." -ForegroundColor Green
