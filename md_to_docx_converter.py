#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Markdown to DOCX Converter
将v8_final.md转换为docx格式
"""

import re
import os
from pathlib import Path

def install_required_packages():
    """安装必需的包"""
    import subprocess
    import sys
    
    packages = ['python-docx', 'markdown']
    
    for package in packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📦 正在安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} 安装完成")

def convert_md_to_docx(md_file_path, output_path=None):
    """将Markdown文件转换为DOCX"""
    
    # 安装必需的包
    install_required_packages()
    
    try:
        from docx import Document
        from docx.shared import Inches, Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.enum.style import WD_STYLE_TYPE
        import markdown
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    
    # 读取Markdown文件
    try:
        with open(md_file_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
    except FileNotFoundError:
        print(f"❌ 文件未找到: {md_file_path}")
        return False
    
    # 创建Word文档
    doc = Document()
    
    # 设置页面边距
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1.25)
        section.right_margin = Inches(1.25)
    
    # 处理Markdown内容
    lines = md_content.split('\n')
    
    for line in lines:
        line = line.strip()
        
        if not line:
            # 空行
            doc.add_paragraph()
            continue
        
        # 处理标题
        if line.startswith('#'):
            level = len(line) - len(line.lstrip('#'))
            title_text = line.lstrip('#').strip()
            
            if level == 1:
                # 一级标题
                p = doc.add_heading(title_text, level=1)
                p.alignment = WD_ALIGN_PARAGRAPH.CENTER
            elif level == 2:
                # 二级标题
                p = doc.add_heading(title_text, level=2)
            elif level == 3:
                # 三级标题
                p = doc.add_heading(title_text, level=3)
            else:
                # 其他级别标题
                p = doc.add_heading(title_text, level=min(level, 9))
        
        # 处理表格（简单处理）
        elif line.startswith('|'):
            # 这里可以添加表格处理逻辑
            p = doc.add_paragraph(line)
            p.style = 'Normal'
        
        # 处理代码块
        elif line.startswith('```'):
            # 跳过代码块标记，添加说明
            if 'mermaid' in line:
                p = doc.add_paragraph('[Mermaid图表 - 请在原始文档中查看]')
                p.style = 'Intense Quote'
            continue
        
        # 处理居中的图表标题
        elif '<div align="center">' in line:
            # 提取图表标题
            title_match = re.search(r'<strong>(.*?)</strong>', line)
            if title_match:
                title_text = title_match.group(1)
                p = doc.add_paragraph(title_text)
                p.alignment = WD_ALIGN_PARAGRAPH.CENTER
                # 设置为图表标题样式
                run = p.runs[0]
                run.bold = True
            continue
        
        # 处理普通段落
        else:
            # 移除Markdown格式标记
            clean_line = line
            clean_line = re.sub(r'\*\*(.*?)\*\*', r'\1', clean_line)  # 粗体
            clean_line = re.sub(r'\*(.*?)\*', r'\1', clean_line)      # 斜体
            clean_line = re.sub(r'\[(.*?)\]\(.*?\)', r'\1', clean_line)  # 链接
            clean_line = re.sub(r'\[(\d+)\]', r'[\1]', clean_line)   # 引用
            
            if clean_line:
                p = doc.add_paragraph(clean_line)
                p.style = 'Normal'
    
    # 保存文档
    if output_path is None:
        output_path = md_file_path.replace('.md', '.docx')
    
    try:
        doc.save(output_path)
        print(f"✅ 转换成功！文件保存为: {output_path}")
        return True
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return False

def main():
    """主函数"""
    print("🔄 Markdown to DOCX 转换器")
    print("=" * 50)
    
    # 检查v8_final.md是否存在
    md_file = "v8_final.md"
    if not os.path.exists(md_file):
        print(f"❌ 文件不存在: {md_file}")
        return
    
    # 转换文件
    output_file = "v8_final.docx"
    success = convert_md_to_docx(md_file, output_file)
    
    if success:
        print("\n🎉 转换完成！")
        print(f"📄 输入文件: {md_file}")
        print(f"📄 输出文件: {output_file}")
        print("\n📝 注意事项:")
        print("- Mermaid图表需要手动插入")
        print("- 表格格式可能需要调整")
        print("- 建议在Word中进行最终格式化")
    else:
        print("\n❌ 转换失败，请检查错误信息")

if __name__ == "__main__":
    main()
