# 修复文字遮挡问题的Mermaid图表

## 问题分析
原始图表中出现文字遮挡的主要原因：
1. **subgraph标题过长**：如"Presentation Layer"、"Model Service Layer"等
2. **英文单词换行**：长英文单词在换行时被截断
3. **节点内容过长**：某些节点包含过多文字

## 解决方案

### 1. 缩短英文标题
- `Presentation Layer` → `Frontend Layer`
- `Model Service Layer` → `AI Service Layer`  
- `Request Preprocessing` → `Preprocessing`
- `Dialogue Understanding` → `Understanding`
- `Knowledge Retrieval` → `Retrieval`
- `Response Generation` → `Generation`
- `Security & Compliance` → `Security`

### 2. 优化节点内容
- `Retrieval-Augmented Generation` → `RAG Generation`
- 去除多余的空格和符号

## 优化后的架构图

<div align="center">

```mermaid
graph TB
    subgraph L1 ["前端交互层<br/>Frontend Layer"]
        A1["Web端<br/>HTTP/WebSocket"]
        A2["移动端<br/>iOS/Android"]
        A3["微信端<br/>小程序/公众号"]
        A4["第三方平台<br/>API接口"]
    end

    subgraph L2 ["模型服务层<br/>AI Service Layer"]
        B1["统一对话理解模块<br/>LLaMA-2-7B<br/>意图识别+槽位提取"]
        B2["知识检索引擎<br/>Faiss向量检索+BM25<br/>混合检索策略"]
        B3["响应生成器<br/>RAG框架<br/>知识增强生成"]
        B4["上下文管理系统<br/>Redis缓存<br/>滑动窗口机制"]
    end

    subgraph L3 ["中间件层<br/>Middleware Layer"]
        C1["消息队列<br/>Apache Kafka<br/>异步通信"]
        C2["监控告警<br/>Prometheus+Grafana<br/>实时监控"]
        C3["流量控制<br/>限流器+熔断器<br/>服务保护"]
        C4["服务治理<br/>Kubernetes<br/>容器编排"]
    end

    subgraph L4 ["数据库层<br/>Storage Layer"]
        D1["会话存储<br/>MySQL 8.0<br/>分库分表"]
        D2["知识库<br/>Elasticsearch<br/>全文检索"]
        D3["系统日志<br/>MongoDB<br/>日志存储"]
        D4["缓存系统<br/>Redis Cluster<br/>高可用缓存"]
    end

    %% 连接关系
    A1 -.-> B1
    A2 -.-> B1
    A3 -.-> B1
    A4 -.-> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4

    B1 -.-> C1
    B2 -.-> C2
    B3 -.-> C3
    B4 -.-> C4

    C1 -.-> D1
    C2 -.-> D2
    C3 -.-> D3
    C4 -.-> D4

    %% 样式设置
    style L1 fill:#e8f4fd,stroke:#1976d2,stroke-width:2px
    style L2 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style L3 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style L4 fill:#fff3e0,stroke:#f57c00,stroke-width:2px
```

</div>

## 修复效果
✅ **解决了文字遮挡问题**
✅ **保持了图表的专业性**
✅ **提升了可读性**
✅ **居中显示效果良好**
