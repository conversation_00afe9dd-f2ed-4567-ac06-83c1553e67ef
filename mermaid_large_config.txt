大尺寸Mermaid图表配置

在mermaid.live中使用以下配置生成更大的图片：

1. 架构图 - 添加以下配置：
%%{init: {'theme':'base', 'themeVariables': {'fontSize': '16px', 'fontFamily': 'Arial', 'primaryColor': '#f9f9f9', 'primaryTextColor': '#333', 'primaryBorderColor': '#333', 'lineColor': '#333'}}}%%

2. 流程图 - 添加以下配置：
%%{init: {'theme':'base', 'themeVariables': {'fontSize': '14px', 'fontFamily': 'Arial', 'primaryColor': '#f9f9f9', 'primaryTextColor': '#333', 'primaryBorderColor': '#333', 'lineColor': '#333'}}}%%

3. 下载设置：
- 格式：PNG
- 建议分辨率：1200px宽度以上
- 确保图片清晰度适合A4打印

4. Word中的最佳设置：
- 宽度：12-15厘米
- 高度：自动调整
- 对齐：居中
- 文字环绕：上下型
