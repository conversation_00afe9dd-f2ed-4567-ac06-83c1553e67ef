# Mermaid图表测试

## 优化后的智能客服系统架构图

<div align="center">

```mermaid
graph TB
    subgraph L1 ["前端交互层<br/>Presentation Layer"]
        A1["Web端<br/>HTTP/WebSocket"]
        A2["移动端<br/>iOS/Android"]
        A3["微信端<br/>小程序/公众号"]
        A4["第三方平台<br/>API接口"]
    end

    subgraph L2 ["模型服务层<br/>Model Service Layer"]
        B1["统一对话理解模块<br/>LLaMA-2-7B<br/>意图识别+槽位提取"]
        B2["知识检索引擎<br/>Faiss向量检索+BM25<br/>混合检索策略"]
        B3["响应生成器<br/>RAG框架<br/>知识增强生成"]
        B4["上下文管理系统<br/>Redis缓存<br/>滑动窗口机制"]
    end

    subgraph L3 ["中间件层<br/>Middleware Layer"]
        C1["消息队列<br/>Apache Kafka<br/>异步通信"]
        C2["监控告警<br/>Prometheus+Grafana<br/>实时监控"]
        C3["流量控制<br/>限流器+熔断器<br/>服务保护"]
        C4["服务治理<br/>Kubernetes<br/>容器编排"]
    end

    subgraph L4 ["数据库层<br/>Data Layer"]
        D1["会话存储<br/>MySQL 8.0<br/>分库分表"]
        D2["知识库<br/>Elasticsearch<br/>全文检索"]
        D3["系统日志<br/>MongoDB<br/>日志存储"]
        D4["缓存系统<br/>Redis Cluster<br/>高可用缓存"]
    end

    %% 前端到模型服务层的连接
    A1 -.-> B1
    A2 -.-> B1
    A3 -.-> B1
    A4 -.-> B1

    %% 模型服务层内部流程
    B1 --> B2
    B2 --> B3
    B3 --> B4

    %% 模型服务层到中间件层的连接
    B1 -.-> C1
    B2 -.-> C2
    B3 -.-> C3
    B4 -.-> C4

    %% 中间件层到数据库层的连接
    C1 -.-> D1
    C2 -.-> D2
    C3 -.-> D3
    C4 -.-> D4

    %% 层级样式
    style L1 fill:#e8f4fd,stroke:#1976d2,stroke-width:2px
    style L2 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style L3 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style L4 fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    %% 节点样式
    style A1 fill:#ffffff,stroke:#1976d2,stroke-width:1px
    style A2 fill:#ffffff,stroke:#1976d2,stroke-width:1px
    style A3 fill:#ffffff,stroke:#1976d2,stroke-width:1px
    style A4 fill:#ffffff,stroke:#1976d2,stroke-width:1px

    style B1 fill:#ffffff,stroke:#7b1fa2,stroke-width:1px
    style B2 fill:#ffffff,stroke:#7b1fa2,stroke-width:1px
    style B3 fill:#ffffff,stroke:#7b1fa2,stroke-width:1px
    style B4 fill:#ffffff,stroke:#7b1fa2,stroke-width:1px

    style C1 fill:#ffffff,stroke:#388e3c,stroke-width:1px
    style C2 fill:#ffffff,stroke:#388e3c,stroke-width:1px
    style C3 fill:#ffffff,stroke:#388e3c,stroke-width:1px
    style C4 fill:#ffffff,stroke:#388e3c,stroke-width:1px

    style D1 fill:#ffffff,stroke:#f57c00,stroke-width:1px
    style D2 fill:#ffffff,stroke:#f57c00,stroke-width:1px
    style D3 fill:#ffffff,stroke:#f57c00,stroke-width:1px
    style D4 fill:#ffffff,stroke:#f57c00,stroke-width:1px
```

</div>

## 优化说明

这个优化版本解决了以下问题：
1. **居中显示**：使用 `<div align="center">` 包装图表
2. **文字完整显示**：将长标题分成两行显示，避免文字被截断
3. **颜色优化**：使用不同颜色区分各层，提升视觉效果
4. **布局优化**：去除多余空格，减少文字遮挡
5. **代码可读性**：添加了注释，使代码结构更清晰
