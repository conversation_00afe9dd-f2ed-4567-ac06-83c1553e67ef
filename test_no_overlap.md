# 测试无遮挡版本

## 方案1：纯中文标题（推荐）

```mermaid
graph TB
    subgraph L1 ["前端交互层"]
        A1["Web端<br/>HTTP/WebSocket"]
        A2["移动端<br/>iOS/Android"]
        A3["微信端<br/>小程序/公众号"]
        A4["第三方平台<br/>API接口"]
    end

    subgraph L2 ["模型服务层"]
        B1["统一对话理解模块<br/>LLaMA-2-7B<br/>意图识别+槽位提取"]
        B2["知识检索引擎<br/>Faiss向量检索+BM25<br/>混合检索策略"]
        B3["响应生成器<br/>RAG框架<br/>知识增强生成"]
        B4["上下文管理系统<br/>Redis缓存<br/>滑动窗口机制"]
    end

    subgraph L3 ["中间件层"]
        C1["消息队列<br/>Apache Kafka<br/>异步通信"]
        C2["监控告警<br/>Prometheus+Grafana<br/>实时监控"]
        C3["流量控制<br/>限流器+熔断器<br/>服务保护"]
        C4["服务治理<br/>Kubernetes<br/>容器编排"]
    end

    subgraph L4 ["数据库层"]
        D1["会话存储<br/>MySQL 8.0<br/>分库分表"]
        D2["知识库<br/>Elasticsearch<br/>全文检索"]
        D3["系统日志<br/>MongoDB<br/>日志存储"]
        D4["缓存系统<br/>Redis Cluster<br/>高可用缓存"]
    end

    %% 连接关系
    A1 -.-> B1
    A2 -.-> B1
    A3 -.-> B1
    A4 -.-> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4

    B1 -.-> C1
    B2 -.-> C2
    B3 -.-> C3
    B4 -.-> C4

    C1 -.-> D1
    C2 -.-> D2
    C3 -.-> D3
    C4 -.-> D4

    %% 样式设置
    style L1 fill:#e8f4fd,stroke:#1976d2,stroke-width:2px
    style L2 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style L3 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style L4 fill:#fff3e0,stroke:#f57c00,stroke-width:2px
```

## 方案2：如果还有问题，使用更短的标题

```mermaid
graph TB
    subgraph L1 ["前端层"]
        A1["Web端<br/>HTTP/WebSocket"]
        A2["移动端<br/>iOS/Android"]
        A3["微信端<br/>小程序/公众号"]
        A4["第三方平台<br/>API接口"]
    end

    subgraph L2 ["服务层"]
        B1["统一对话理解模块<br/>LLaMA-2-7B<br/>意图识别+槽位提取"]
        B2["知识检索引擎<br/>Faiss向量检索+BM25<br/>混合检索策略"]
        B3["响应生成器<br/>RAG框架<br/>知识增强生成"]
        B4["上下文管理系统<br/>Redis缓存<br/>滑动窗口机制"]
    end

    subgraph L3 ["中间层"]
        C1["消息队列<br/>Apache Kafka<br/>异步通信"]
        C2["监控告警<br/>Prometheus+Grafana<br/>实时监控"]
        C3["流量控制<br/>限流器+熔断器<br/>服务保护"]
        C4["服务治理<br/>Kubernetes<br/>容器编排"]
    end

    subgraph L4 ["存储层"]
        D1["会话存储<br/>MySQL 8.0<br/>分库分表"]
        D2["知识库<br/>Elasticsearch<br/>全文检索"]
        D3["系统日志<br/>MongoDB<br/>日志存储"]
        D4["缓存系统<br/>Redis Cluster<br/>高可用缓存"]
    end

    %% 连接关系
    A1 -.-> B1
    A2 -.-> B1
    A3 -.-> B1
    A4 -.-> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4

    B1 -.-> C1
    B2 -.-> C2
    B3 -.-> C3
    B4 -.-> C4

    C1 -.-> D1
    C2 -.-> D2
    C3 -.-> D3
    C4 -.-> D4

    %% 样式设置
    style L1 fill:#e8f4fd,stroke:#1976d2,stroke-width:2px
    style L2 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style L3 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style L4 fill:#fff3e0,stroke:#f57c00,stroke-width:2px
```
